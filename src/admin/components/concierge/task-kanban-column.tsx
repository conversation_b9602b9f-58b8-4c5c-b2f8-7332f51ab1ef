import React from "react";
import { useDroppable } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { Heading, Text, Badge } from "@camped-ai/ui";
import {
  Clock,
  Pause,
  AlertTriangle,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { TaskScreenData } from "../../routes/concierge-management/tasks/loader";
import KanbanTaskCard from "./kanban-task-card";

// Status-based icons for task columns
const getStatusIcon = (status: string) => {
  const statusIcons = {
    pending: Clock,
    in_progress: Pause,
    review: AlertTriangle,
    completed: CheckCircle,
    cancelled: XCircle,
  } as const;
  
  return statusIcons[status as keyof typeof statusIcons] || Clock;
};

// Status-based colors for task columns
const getStatusColor = (status: string) => {
  const statusColors = {
    pending: 'orange',
    in_progress: 'blue',
    review: 'purple',
    completed: 'green',
    cancelled: 'red',
  } as const;
  
  return statusColors[status as keyof typeof statusColors] || 'grey';
};

// Status-based background styles
const getStatusStyles = (status: string) => {
  const styles = {
    pending: {
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
    },
    in_progress: {
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
    },
    review: {
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
    },
    completed: {
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
    },
    cancelled: {
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
    },
  } as const;
  
  return styles[status as keyof typeof styles] || {
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
  };
};

// Format status for display
const formatStatus = (status: string) => {
  return status
    .replace('_', ' ')
    .replace(/\b\w/g, (l) => l.toUpperCase());
};

interface TaskKanbanColumnProps {
  status: string;
  tasks: TaskScreenData[];
  onEdit?: (task: TaskScreenData) => void;
  onDelete?: (task: TaskScreenData) => void;
  onCopyId?: (task: TaskScreenData) => void;
  hasEditPermission?: boolean;
  hasDeletePermission?: boolean;
  updatingTaskId?: string | null;
}

const TaskKanbanColumn: React.FC<TaskKanbanColumnProps> = ({
  status,
  tasks,
  onEdit,
  onDelete,
  onCopyId,
  hasEditPermission = false,
  hasDeletePermission = false,
  updatingTaskId = null,
}) => {
  const IconComponent = getStatusIcon(status);
  const color = getStatusColor(status).replace('gray', 'grey') as
    | 'orange'
    | 'blue'
    | 'purple'
    | 'green'
    | 'red'
    | 'grey';
  const { bgColor, borderColor } = getStatusStyles(status);

  const { setNodeRef, isOver } = useDroppable({
    id: status,
    data: {
      type: "column",
      status,
    },
  });

  // Guard against undefined/null tasks
  const safeTasks = Array.isArray(tasks) ? tasks : [];
  const taskIds = safeTasks.map((task) => task.id);

  return (
    <div className="flex flex-col h-full min-w-[280px] max-w-[320px]">
      {/* Column Header */}
      <div className={`
        flex items-center justify-between p-4 rounded-t-lg border-t border-l border-r
        ${bgColor} ${borderColor}
      `}>
        <div className="flex items-center gap-2">
          <IconComponent className="h-4 w-4" />
          <Heading level="h3" className="text-sm font-medium">
            {formatStatus(status)}
          </Heading>
        </div>
        <Badge color={color} size="small">
          {safeTasks.length}
        </Badge>
      </div>

      {/* Column Content */}
      <div
        ref={setNodeRef}
        className={`
          flex-1 p-3 border-l border-r border-b rounded-b-lg min-h-[400px]
          ${bgColor} ${borderColor}
          ${isOver ? 'bg-opacity-80 ring-2 ring-blue-300' : ''}
          transition-all duration-200
        `}
      >
        <SortableContext items={taskIds} strategy={verticalListSortingStrategy}>
          <div className="space-y-3">
            {safeTasks.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <IconComponent className="h-8 w-8 text-ui-fg-muted mb-2" />
                <Text className="text-sm text-ui-fg-muted">
                  No {formatStatus(status).toLowerCase()} tasks
                </Text>
              </div>
            ) : (
              safeTasks.map((task) => (
                <KanbanTaskCard
                  key={task.id}
                  task={task}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onCopyId={onCopyId}
                  hasEditPermission={hasEditPermission}
                  hasDeletePermission={hasDeletePermission}
                  isUpdating={updatingTaskId === task.id}
                />
              ))
            )}
          </div>
        </SortableContext>
      </div>
    </div>
  );
};

export default TaskKanbanColumn;
