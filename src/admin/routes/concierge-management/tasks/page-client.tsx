import React, { useState, useMemo } from "react";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { format } from "date-fns";
import {
  Badge,
  Button,
  Container,
  Heading,
  Text,
  toast,
  DropdownMenu,
  usePrompt,
} from "@camped-ai/ui";
import { Edit, Eye, MoreHorizontal, Trash, Plus, Calendar } from "lucide-react";
import {
  useReactTable,
  getCoreRowModel,
  create<PERSON><PERSON>umn<PERSON>elper,
  ColumnDef,
} from "@tanstack/react-table";
import { useQueryClient } from "@tanstack/react-query";
import { useRbac } from "../../../hooks/use-rbac";
import { DataTable } from "../../../../components/table/data-table";
import type { Filter } from "../../../../components/table/data-table";
import { TaskFormModal } from "../../../components/concierge/task-form-modal";
import { UserDisplayName } from "../../../components/concierge/user-display-name";
import {
  useDeleteConciergeTask,
  useUpdateConciergeTask,
} from "../../../hooks/api/concierge-tasks";

import { TaskKanbanView } from "../../../components/concierge/task-kanban-view";
import { ViewToggle } from "../../../components/concierge/view-toggle";
import { useViewMode } from "../../../hooks/use-view-mode";
import { useRealTimeTasks } from "../../../hooks/use-real-time-tasks";
import type { FilterState } from "../../../components/concierge/task-filter-controls";
import type { SortState } from "../../../components/concierge/task-sort-controls";
import type { TaskScreenData } from "./loader";
import "../../../styles/task-modal.css";

// Status badge colors
const statusColors = {
  pending: "orange",
  in_progress: "blue",
  review: "orange",
  completed: "green",
  cancelled: "grey",
} as const;

const priorityColors = {
  low: "grey",
  medium: "blue",
  high: "orange",
  urgent: "red",
} as const;

interface TasksPageClientProps {
  tasks: TaskScreenData[];
  isLoading: boolean;
  totalCount: number;
  pageSize: number;
}

const TasksPageClient: React.FC<TasksPageClientProps> = ({
  tasks,
  isLoading,
  totalCount,
  pageSize,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { hasPermission } = useRbac();
  const prompt = usePrompt();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<TaskScreenData | null>(null);

  // View mode state management
  const { viewMode, setViewMode } = useViewMode({
    defaultMode: "table",
    storageKey: "concierge-tasks-view-mode",
  });

  // Card view handlers
  const handleFiltersChange = (filters: FilterState) => {
    // Filters are handled by URL params, so this is mainly for triggering re-renders
    // The actual filtering happens at the route level via URL parameters
  };

  const handleSortChange = (sort: SortState) => {
    // Sort is handled by URL params, so this is mainly for triggering re-renders
    // The actual sorting happens at the route level via URL parameters
  };

  const handlePageChange = (page: number) => {
    const newSearchParams = new URLSearchParams(location.search);
    if (page === 1) {
      newSearchParams.delete("page");
    } else {
      newSearchParams.set("page", page.toString());
    }
    navigate(`${location.pathname}?${newSearchParams.toString()}`, {
      replace: true,
    });
  };

  const handlePageSizeChange = (newPageSize: number) => {
    const newSearchParams = new URLSearchParams(location.search);
    newSearchParams.set("limit", newPageSize.toString());
    newSearchParams.delete("page"); // Reset to page 1
    navigate(`${location.pathname}?${newSearchParams.toString()}`, {
      replace: true,
    });
  };

  // API hooks
  const deleteTaskMutation = useDeleteConciergeTask();
  const updateTaskMutation = useUpdateConciergeTask();
  const queryClient = useQueryClient();

  // Column helper for type safety
  const columnHelper = createColumnHelper<TaskScreenData>();

  // Helper functions
  const getStatusBadgeVariant = (status: string) => {
    return statusColors[status as keyof typeof statusColors] || "grey";
  };

  const getPriorityBadgeVariant = (priority: string) => {
    return priorityColors[priority as keyof typeof priorityColors] || "grey";
  };

  const handleCopyTaskId = (taskId: string) => {
    navigator.clipboard.writeText(taskId);
    toast.success("Task ID copied to clipboard");
  };

  // Wrapper for kanban view that expects task object
  const handleCopyTaskIdFromTask = (task: TaskScreenData) => {
    handleCopyTaskId(task.id);
  };

  // Handle task status change for kanban drag and drop
  const handleTaskStatusChange = async (taskId: string, newStatus: string) => {
    // Store the original task data for potential rollback
    const originalTaskData = queryClient.getQueryData([
      "task-screen",
      filters,
    ]) as any;
    const originalTask = originalTaskData?.tasks?.find(
      (task: TaskScreenData) => task.id === taskId
    );

    // If task is not found in cache, try to find it in the tasks prop as fallback
    const fallbackTask =
      originalTask || tasks.find((task) => task.id === taskId);

    if (!fallbackTask) {
      console.warn(
        `Task ${taskId} not found in cache or props, proceeding with API call only`
      );
    }

    // OPTIMISTIC UPDATE: Immediately update all relevant caches
    const updateTaskInCache = (
      queryKey: any[],
      updater: (oldData: any) => any
    ) => {
      queryClient.setQueryData(queryKey, (oldData: any) => {
        if (!oldData || !oldData.tasks) return oldData;

        return {
          ...oldData,
          tasks: oldData.tasks.map((task: any) =>
            task.id === taskId ? updater(task) : task
          ),
        };
      });
    };

    // Update the main task-screen cache with current filters
    updateTaskInCache(["task-screen", filters], (task: TaskScreenData) => ({
      ...task,
      status: newStatus,
      updated_at: new Date().toISOString(), // Update timestamp optimistically
    }));

    // Update individual task cache
    queryClient.setQueryData(["concierge-task", taskId], (oldData: any) => {
      if (!oldData) return oldData;
      return {
        ...oldData,
        status: newStatus,
        updated_at: new Date().toISOString(),
      };
    });

    // Update any other task-screen caches that might exist with different filters
    const queryCache = queryClient.getQueryCache();
    queryCache.findAll({ queryKey: ["task-screen"] }).forEach((query) => {
      if (query.queryKey[1] !== filters) {
        // Different filters
        updateTaskInCache(query.queryKey, (task: TaskScreenData) => ({
          ...task,
          status: newStatus,
          updated_at: new Date().toISOString(),
        }));
      }
    });

    // Update concierge-tasks caches
    queryCache.findAll({ queryKey: ["concierge-tasks"] }).forEach((query) => {
      updateTaskInCache(query.queryKey, (task: any) => ({
        ...task,
        status: newStatus,
        updated_at: new Date().toISOString(),
      }));
    });

    try {
      // Make the API call
      const response = await fetch(
        `/admin/concierge-management/tasks/${taskId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({
            status: newStatus,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update task status");
      }

      const updatedTask = await response.json();

      // SUCCESS: Update caches with the actual server response
      updateTaskInCache(
        ["task-screen", filters],
        () => updatedTask.task || updatedTask
      );

      queryClient.setQueryData(
        ["concierge-task", taskId],
        updatedTask.task || updatedTask
      );

      // Update other caches with server data
      queryCache.findAll({ queryKey: ["task-screen"] }).forEach((query) => {
        if (query.queryKey[1] !== filters) {
          updateTaskInCache(
            query.queryKey,
            () => updatedTask.task || updatedTask
          );
        }
      });

      queryCache.findAll({ queryKey: ["concierge-tasks"] }).forEach((query) => {
        updateTaskInCache(
          query.queryKey,
          () => updatedTask.task || updatedTask
        );
      });

      return updatedTask;
    } catch (error) {
      // FAILURE: Rollback all optimistic updates if we have the original task data
      if (fallbackTask) {
        const rollbackTask = () => fallbackTask;

        updateTaskInCache(["task-screen", filters], rollbackTask);
        queryClient.setQueryData(["concierge-task", taskId], fallbackTask);

        queryCache.findAll({ queryKey: ["task-screen"] }).forEach((query) => {
          if (query.queryKey[1] !== filters) {
            updateTaskInCache([...query.queryKey], rollbackTask);
          }
        });

        queryCache
          .findAll({ queryKey: ["concierge-tasks"] })
          .forEach((query) => {
            updateTaskInCache([...query.queryKey], rollbackTask);
          });
      } else {
        // If we don't have original task data, just invalidate the queries to refetch
        queryClient.invalidateQueries({ queryKey: ["task-screen"] });
        queryClient.invalidateQueries({ queryKey: ["concierge-tasks"] });
        queryClient.invalidateQueries({ queryKey: ["concierge-task", taskId] });
      }

      // Re-throw error for kanban component to handle
      throw error;
    }
  };

  // Handle delete task with confirmation
  const handleDelete = async (task: TaskScreenData) => {
    const confirmed = await prompt({
      title: "Delete Task",
      description: `Are you sure you want to delete the task "${task.title}"? This action cannot be undone.`,
      confirmText: "Delete",
      cancelText: "Cancel",
    });

    if (confirmed) {
      // Dismiss any existing toasts and show loading toast
      toast.dismiss();
      const loadingToast = toast.loading("Deleting task...");

      try {
        await deleteTaskMutation.mutateAsync(task.id);
        toast.dismiss(loadingToast);
        toast.success("Task deleted successfully");
      } catch (error) {
        toast.dismiss(loadingToast);
        toast.error("Failed to delete task");
      }
    }
  };

  // Define columns
  const columns = useMemo<ColumnDef<TaskScreenData, any>[]>(
    () => [
      columnHelper.display({
        id: "task",
        header: "Task",
        cell: ({ row }) => {
          const task = row.original;
          return (
            <div className="flex items-center gap-x-3 w-[200px] truncate">
              <div className="flex h-8 w-8 items-center justify-center rounded bg-ui-bg-subtle">
                <Calendar className="h-4 w-4 text-ui-fg-subtle" />
              </div>
              <div>
                <Text className="txt-compact-medium-plus" weight="plus">
                  {task.title}
                </Text>
                <div className="flex items-center gap-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCopyTaskId(task.id);
                    }}
                    className="text-xs text-ui-fg-muted hover:text-ui-fg-subtle"
                  >
                    {task.id.slice(0, 8)}...
                  </button>
                </div>
              </div>
            </div>
          );
        },
      }),
      columnHelper.accessor("description", {
        header: "Description",
        cell: ({ row }) => (
          <div className="w-[200px] truncate">
            <Text className="text-ui-fg-subtle">
              {row.original.description || "No description"}
            </Text>
          </div>
        ),
      }),
      columnHelper.accessor("status", {
        header: "Status",
        cell: ({ row }) => (
          <Badge
            color={getStatusBadgeVariant(row.original.status)}
            size="xsmall"
          >
            {row.original.status
              .replace("_", " ")
              .replace(/\b\w/g, (l) => l.toUpperCase())}
          </Badge>
        ),
      }),
      columnHelper.accessor("priority", {
        header: "Priority",
        cell: ({ row }) => (
          <Badge
            color={getPriorityBadgeVariant(row.original.priority)}
            size="xsmall"
          >
            {row.original.priority.charAt(0).toUpperCase() +
              row.original.priority.slice(1)}
          </Badge>
        ),
      }),
      columnHelper.accessor("entity_type", {
        header: "Entity Type",
        cell: ({ row }) => (
          <Text>
            {row.original.entity_type
              ? row.original.entity_type.charAt(0).toUpperCase() +
                row.original.entity_type.slice(1)
              : "General"}
          </Text>
        ),
      }),
      columnHelper.accessor("assigned_to", {
        header: "Assigned To",
        cell: ({ row }) => (
          <UserDisplayName userId={row.original.assigned_to} />
        ),
      }),
      columnHelper.accessor("due_date", {
        header: "Due Date",
        cell: ({ row }) => (
          <Text className={row.original.due_date ? "" : "text-ui-fg-subtle"}>
            {row.original.due_date
              ? format(new Date(row.original.due_date), "MMM dd, yyyy")
              : "No due date"}
          </Text>
        ),
      }),
      columnHelper.accessor("created_at", {
        header: "Created",
        cell: ({ row }) => (
          <Text>
            {format(new Date(row.original.created_at), "MMM dd, yyyy")}
          </Text>
        ),
      }),
      columnHelper.display({
        id: "actions",
        header: "",
        cell: ({ row }) => {
          const task = row.original;
          return (
            <DropdownMenu>
              <DropdownMenu.Trigger asChild>
                <Button
                  variant="secondary"
                  size="small"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content>
                <DropdownMenu.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(`/concierge-management/tasks/${task.id}`);
                  }}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View
                </DropdownMenu.Item>
                {hasPermission("concierge_management:edit") && (
                  <DropdownMenu.Item
                    onClick={(e) => {
                      e.stopPropagation();
                      setEditingTask(task);
                    }}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenu.Item>
                )}
                {hasPermission("concierge_management:delete") && (
                  <DropdownMenu.Item
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(task);
                    }}
                    className="text-red-600"
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenu.Item>
                )}
              </DropdownMenu.Content>
            </DropdownMenu>
          );
        },
      }),
    ],
    [hasPermission, navigate]
  );

  // Define filters
  const filters: Filter[] = useMemo(
    () => [
      {
        key: "status",
        label: "Status",
        type: "select",
        options: [
          { label: "Pending", value: "pending" },
          { label: "In Progress", value: "in_progress" },
          { label: "Review", value: "review" },
          { label: "Completed", value: "completed" },
          { label: "Cancelled", value: "cancelled" },
        ],
      },
      {
        key: "priority",
        label: "Priority",
        type: "select",
        options: [
          { label: "Low", value: "low" },
          { label: "Medium", value: "medium" },
          { label: "High", value: "high" },
          { label: "Urgent", value: "urgent" },
        ],
      },
      {
        key: "entity_type",
        label: "Entity Type",
        type: "select",
        options: [
          { label: "Booking", value: "booking" },
          { label: "Deal", value: "deal" },
          { label: "Guest", value: "guest" },
          { label: "Itinerary", value: "itinerary" },
        ],
      },
    ],
    []
  );

  // Define sortable columns
  const orderBy = useMemo(
    () => [
      { key: "title" as keyof TaskScreenData, label: "Title" },
      { key: "status" as keyof TaskScreenData, label: "Status" },
      { key: "priority" as keyof TaskScreenData, label: "Priority" },
      { key: "due_date" as keyof TaskScreenData, label: "Due Date" },
      { key: "created_at" as keyof TaskScreenData, label: "Created At" },
    ],
    []
  );

  // Get current page from URL
  const searchParams = new URLSearchParams(location.search);
  const currentPage = parseInt(searchParams.get("page") || "1");

  // Real-time synchronization - only enable when page is visible and not loading
  useRealTimeTasks({
    enabled: !isLoading && typeof window !== "undefined",
    pollingInterval: 30000, // 30 seconds
    filters: {
      limit: pageSize,
      offset: (currentPage - 1) * pageSize,
      // Add other relevant filters here if needed
    },
  });

  // Create table instance
  const table = useReactTable({
    data: tasks,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: Math.ceil((totalCount || tasks.length) / pageSize),
    state: {
      pagination: {
        pageIndex: currentPage - 1,
        pageSize,
      },
    },
  });

  return (
    <Container className="divide-y p-0">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <Heading level="h2">Tasks</Heading>
        </div>
        {/* <div className="flex items-center gap-3">
          <ViewToggle viewMode={viewMode} onViewModeChange={setViewMode} />
        </div> */}
      </div>

      {/* Content - Table, Card, or Kanban View */}
      {viewMode === "table" ? (
        <DataTable
          table={table}
          columns={columns}
          pageSize={pageSize}
          count={totalCount || tasks.length}
          isLoading={isLoading}
          filters={filters}
          orderBy={orderBy}
          search="autofocus"
          pagination
          navigateTo={(row) => `/concierge-management/tasks/${row.original.id}`}
          queryObject={Object.fromEntries(searchParams)}
          noRecords={{
            title: "No tasks found",
            message: "Get started by creating your first task",
          }}
        />
      ) : (
        <TaskKanbanView
          tasks={tasks}
          isLoading={isLoading}
          totalCount={totalCount}
          onEdit={setEditingTask}
          onDelete={handleDelete}
          onCopyId={handleCopyTaskIdFromTask}
          onFiltersChange={handleFiltersChange}
          onSortChange={handleSortChange}
          onStatusChange={handleTaskStatusChange}
          hasEditPermission={hasPermission("concierge_management:edit")}
          hasDeletePermission={hasPermission("concierge_management:delete")}
          noRecordsTitle="No tasks found"
          noRecordsMessage="Get started by creating your first task"
        />
      )}

      {/* Create Task Modal */}
      <TaskFormModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
      />

      {/* Edit Task Modal */}
      <TaskFormModal
        isOpen={!!editingTask}
        onClose={() => setEditingTask(null)}
        task={editingTask || undefined}
      />
    </Container>
  );
};

export default TasksPageClient;
